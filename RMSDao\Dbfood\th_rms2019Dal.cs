using RMSModel.DbFood;
using RMSModel.RMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RMSDao.Dbfood
{
    /// <summary>
    ///th_rms2019存储过程操作
    /// </summary>
    public static class th_rms2019Dal
    {
        static string sql = "th_rms2019";

        public static void SetConnection(string connection)
        {
            if (string.IsNullOrEmpty(DbHelp_dbfood<object>.dao.connstr) == true)
            {

                DbHelp_dbfood<Room>.dao.connstr = "Data Source=" + connection + ";Initial Catalog=dbfood;User ID=sa;Password= ";
                DbHelp_dbfood<BookCacheInfo>.dao.connstr = "Data Source=" + connection + ";Initial Catalog=dbfood;User ID=sa;Password= ";


            }


        }
        /// <summary>
        /// 获取所有的房间信息
        /// </summary>
        public static List<Room> GetAllRoomInfo()
        {
            return DbHelp_dbfood<Room>.dao.proReturnList(sql, new { type = 1 });
        }
        /// <summary>
        /// 开房操
        /// </summary>
        /// <param name="RmNo">房号</param>
        /// <param name="CustName">顾客姓名</param>
        /// <param name="Number">人数</param>
        /// <returns></returns>
        public static Room SetOpenRoom(string RmNo, string CustName, int Number, string rtNo,bool giftFromVip)
        {
            return DbHelp_dbfood<Room>.dao.pro(sql, new { type = 2, RmNo = RmNo, CustName = @CustName, Number = @Number, rtNo, giftFromVip });
        }
        /// <summary>
        /// 清洁操作
        /// </summary>
        /// <param name="RmNo">房号</param>

        /// <returns></returns>
        public static List<Room> SetClearingToEmptyRoom(string RmNo)
        {
            return DbHelp_dbfood<Room>.dao.proReturnList(sql, new { type = 5, RmNo = RmNo });
        }
        /// <summary>
        /// 转房操作
        /// </summary>
        /// <param name="FromRmNo">转出房间</param>
        /// <param name="ToRmNo">转入房间</param>
        /// <returns></returns>
        public static List<Room> Rm_Exchange(string FromRmNo, string ToRmNo)
        {
            return DbHelp_dbfood<Room>.dao.proReturnList(sql, new { type = 3, FromRmNo = FromRmNo, ToRmNo = ToRmNo });
        }
        /// <summary>
        /// 坏房标识
        /// </summary>
        /// <param name="RmNo">房间</param>
        /// <param name="IfSetBad">操作：true:坏房，false：取消坏房</param>
        /// <param name="Reason">原因</param>
        /// <returns></returns>
        public static void Rm_SetBad(string RmNo, bool IfSetBad, string Reason)
        {
            DbHelp_dbfood<Room>.dao.proReturnList(sql, new { type = 4, Reason = Reason, RmNo = RmNo, IfSetBad = IfSetBad });
        }
        /// <summary>
        /// 续单
        /// </summary>
        /// <param name="RmNo">房间</param>
        /// <returns></returns>
        public static void Rm_Rm_Continue(string RmNo)
        {
            DbHelp_dbfood<Room>.dao.proReturnList(sql, new { type = 6, RmNo = RmNo });
        }

        /// <summary>
        /// 全部清房
        /// </summary>
        /// <param name="RmNo">房间</param>
        /// <param name="IfSetBad">操作：true:坏房，false：取消坏房</param>
        /// <param name="Reason">原因</param>
        /// <returns></returns>
        public static void SetRoomClear(string room)
        {
            if (room != null)
            {
                DbHelp_dbfood<Room>.dao.OperationData("update room set invno='',workdate='',rmstatus='E',indate='',intime='',openuserid='',custname='',orderuserid='' where rmno=@rmno ", new { rmno = room });

            }
            else
            {
                DbHelp_dbfood<Room>.dao.OperationData("update room set invno='',workdate='',rmstatus='E',indate='',intime='',openuserid='',custname='',orderuserid='' where rmno not like '%B' ");
            }
        }

        public static List<BookCacheInfo> GetBookInfo(string Date, string CustTel)
        {
            return DbHelp_dbfood<BookCacheInfo>.dao.sqlDataToList("select * from rms2009.dbo.BookCacheInfo where ComeDate=@Date and CustTel=@CustTel", new { Date = Date, CustTel = CustTel });
        }

        public static List<BookCacheInfo> GetBookInfo_all(string Date, string Shopid)
        {
            return DbHelp_dbfood<BookCacheInfo>.dao.sqlDataToList("select * from rms2009.dbo.BookCacheInfo where ComeDate=@Date and Shopid=@Shopid", new { Date = Date, Shopid = Shopid });
        }


        public static List<Room> GetRoom_All()
        {
            return DbHelp_dbfood<Room>.dao.sqlDataToList("select * from Room where rmno not like '%B' ");
        }

        public static T trigger_synchro<T>()
        {
            return DbHelp_dbfood<T>.dao.pro("trigger_synchro");
        }


        public static List<RmClose> GetLocalRmsRoom(string ShopId)
        {
          //  return DbHelp_dbfood<RmClose>.dao.sqlDataToList(" select CloseTime,RmNo,OpenStatus,ConStatus,InvNo,RmsStatus,dbfoodStatus,RoomToIkey,kFee from  RmsRoom  where ShopId=@ShopId ", new { ShopId });


            return DbHelp_dbfood<RmClose>.dao.sqlDataToList(@"   select CloseTime,a.RmNo,OpenStatus,ConStatus,a.InvNo,RmsStatus,dbfoodStatus,RoomToIkey,kFee ,b.Beg_Key
from  RmInfo  as a  left join opencacheinfo b on a.RoomToIkey=b.Ikey

 where a.ShopId=@ShopId ", new { ShopId });

         

       
        }
        /// <summary>
        /// 订金查询是否绑定
        /// </summary>
        /// <returns></returns>
        public static Room BindDepositCheck(string out_trade_no)
        {
            return DbHelp_dbfood<Room>.dao.sqlSingleData(@"select RmNo from wxPayInfo where out_trade_no=@out_trade_no", new { out_trade_no = out_trade_no });
        }
        /// <summary>
        /// 订金绑定
        /// </summary>
        /// <returns></returns>
        public static int BindDeposit(int ShopId, string RmNo, string InvNo, int Tot, string transaction_id, string out_trade_no)
        {
            return DbHelp_dbfood<Room>.dao.OperationData(@"Insert into wxPayInfo(ShopId,RmNo,InvNo,Tot,transaction_id,out_trade_no,IsAutoPrnt,PayType,wxPayTot)values
(@ShopId,@RmNo,@InvNo,@Tot,@transaction_id,@out_trade_no,1,1,@Tot)", new { ShopId = ShopId, RmNo = RmNo, InvNo = InvNo, Tot = Tot, transaction_id = transaction_id, out_trade_no = out_trade_no });
        }
        /// <summary>
        /// 插入代订人记录
        /// </summary>
        /// <returns></returns>
        public static int SetInsertOrerUserInfo(Rms2018 rms2018)
        {
            Room room = DbHelp_dbfood<Room>.dao.sqlSingleData(@"select * from  rms2009.dbo.Rms2018  where invno=@InvNo and Ikey=@Ikey", rms2018);
            if (room == null)
            {
                return DbHelp_dbfood<Room>.dao.OperationData(@"Insert into rms2009.dbo.Rms2018 values(@Ikey,@OrderUser,@OrderName,@InvNo)", rms2018);
            }
            else
            {
                return DbHelp_dbfood<Room>.dao.OperationData(@"update  rms2009.dbo.Rms2018  set OrderUser=@OrderUser,OrderName=@OrderName  where invno=@InvNo and Ikey=@Ikey  ", rms2018);

            }

        }

        /// <summary>
        /// 设置RMS2009房间状态
        /// </summary>
        /// <returns></returns>
        public static void SetRms2009Status(string rmstatusnow,int ShopId,string rmno)
        {
           DbHelp_dbfood<Room>.dao.OperationData(@"update Rms2009.dbo.RmInfo set rmstatusnow=@rmstatusnow where ShopId=@ShopId and rmno=@rmno", new { rmstatusnow = rmstatusnow , ShopId = ShopId , rmno = rmno });

        }
        /// <summary>
        /// 获取当天所有开房数据
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.DbFood.FdInvInfo> GetRoomInfo(RMSModel.ReplaceBooking.OpenCacheInfo opendata)
        {
            string sql = "select * from dbfood.dbo.fdinv where WorkDate=@WorkDate and rmno=@RmNo";
            return DbHelp_dbfood<RMSModel.DbFood.FdInvInfo>.dao.sqlDataToList(sql, new { WorkDate = opendata.ComeDate, RmNo = opendata.RmNo });

            //string sql = "select * from OperateData.[dbo].[fdinv] where WorkDate=@WorkDate and ShopId=@ShopId and rmno=@rmno ";
            //return DbHelp_mims<RMSModel.DbFood.FdInvInfo>.dao.sqlDataToList(sql, new { WorkDate = opendata.ComeDate, ShopId = opendata.ShopId, rmno = opendata.RmNo });
        }

        /// <summary>
        /// 执行转房操作（指定计费房型）- 简化版新增功能
        /// </summary>
        /// <param name="fromRmNo">转出房间号</param>
        /// <param name="toRmNo">转入房间号</param>
        /// <param name="billingRtNo">计费房型编号</param>
        /// <param name="userId">操作员ID</param>
        /// <param name="remarks">备注</param>
        /// <returns></returns>
        public static bool ExecuteRoomExchangeWithBillingType(string fromRmNo, string toRmNo, string billingRtNo, string userId, string remarks)
        {
            try
            {
                // 第一步：执行原有的转房操作
                DbHelp_dbfood<Room>.dao.proReturnList(sql, new {
                    type = 3,
                    FromRmNo = fromRmNo,
                    ToRmNo = toRmNo
                });

                // 第二步：更新计费房型
                string updateSql = "UPDATE Room SET UpRtNo = @BillingRtNo WHERE RmNo = @ToRmNo";
                DbHelp_dbfood<object>.dao.sqlDataToList(updateSql, new {
                    BillingRtNo = billingRtNo,
                    ToRmNo = toRmNo
                });

                // 第三步：更新会员信息
                string memberSql = @"
                    UPDATE RoomExtend SET MemberNo = (
                        SELECT ISNULL(MemberNo,'') FROM RoomExtend WHERE RmNo = @FromRmNo
                    ) WHERE RmNo = @ToRmNo";
                DbHelp_dbfood<object>.dao.sqlDataToList(memberSql, new {
                    FromRmNo = fromRmNo,
                    ToRmNo = toRmNo
                });

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"转房操作异常: {ex.Message}", ex);
            }
        }

    }
}
