<UserControl x:Class="RMS2018.Controls.RoomChange"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50*"/>
            <RowDefinition Height="65*"/>
            <RowDefinition Height="431*"/>  
            <RowDefinition Height="65*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <TextBlock Text="转出房号" FontSize="15" TextAlignment="Right" Margin="25,0,0,0" VerticalAlignment="Center" HorizontalAlignment="Left" Width="70"></TextBlock>
        <ComboBox Width="85" Name="Com_OutRoom"  Background="White" DisplayMemberPath="RmNo" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="110,0,0,0"></ComboBox>
        <TextBlock Text="转入房号"  Width="80" TextAlignment="Right" FontSize="15" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="210,0,0,0"></TextBlock>
        <ComboBox Width="85" Name="Com_ChangeToRoom"  Background="White"  DisplayMemberPath="RmNo" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="300,0,0,0"></ComboBox>
        <StackPanel Orientation="Horizontal" Grid.Row="1">
            <Button Content="预转" Name="Btn_Prerotation" ToolTip="预转到列表中" VerticalAlignment="Center" Click="Btn_Prerotation_Click" Width="80" Height="30" Margin="10,0,0,0"></Button>
            <Button Content="转房" Name="Btn_CRoom" Click="Btn_CRoom_Click"  ToolTip="根据下拉框选项转房"  VerticalAlignment="Center" Width="80" Height="30" Margin="20,0,0,0"></Button>
            <Button Content="刷新" Name="Btn_Refresh" VerticalAlignment="Center" Width="80" Height="30" Margin="20,0,0,0" Click="Btn_Refresh_Click"></Button> 
            <Button Content="关闭" Name="Btn_Cancel" VerticalAlignment="Center" Width="80" Height="30" Margin="20,0,0,0" Click="Btn_Cancel_Click"></Button>
        </StackPanel>
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <StackPanel Orientation="Horizontal" Grid.Column="0">
                <Button Content="转房" Name="Btn_CgRoom" ToolTip="转出列表中选中的一行" Click="Btn_CgRoom_Click" VerticalAlignment="Center" Width="80" Height="30" Margin="25,0,0,0"></Button>
                <Button Content="全部转房" Name="Btn_CgAllRoom" ToolTip="转出列表中所有房间" Click="Btn_CgAllRoom_Click" VerticalAlignment="Center" Width="80" Height="30" Margin="50,0,0,0"></Button>
                <Button Content="取消转房" Name="Btn_Cancel_Room" ToolTip="移除列表中选中的一行" Click="Cancel_Room_Click" VerticalAlignment="Center" Width="80" Height="30" Margin="50,0,0,0"></Button>
            </StackPanel>

            <StackPanel Grid.Column="1" Margin="0,10,0,0">
                <TextBlock Text="请选择转房后的计费方式：" FontWeight="Bold"/>
                <RadioButton x:Name="rbKeepRate" Content="按转出房间类型计费" IsChecked="True" Margin="0,5,0,0"/>
                <RadioButton x:Name="rbUpdateRate" Content="按转入房间类型计费" Margin="0,5,0,0"/>
            </StackPanel>
        </Grid>
        
        <DataGrid Grid.Row="2" Margin="2" VerticalScrollBarVisibility="Auto" Name="Grid_CRooms" CanUserAddRows="False"  ColumnWidth="*"  IsReadOnly="True"  AutoGenerateColumns="False" CanUserResizeColumns="False" CanUserReorderColumns="False" CanUserSortColumns="False" >
            <DataGrid.Columns>
                <DataGridTextColumn Header="转出房号" Width="60*" Binding="{Binding FromRmNo}" ></DataGridTextColumn>
                <DataGridTextColumn Header="转入房号" Width="60*" Binding="{Binding ToRmNo}" ></DataGridTextColumn>
                <DataGridTextColumn Header="经手人" Width="60*" Binding="{Binding UserName}" ></DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>

        <DataGrid Grid.Column="1" Grid.RowSpan="4" VerticalScrollBarVisibility="Auto" Margin="2" Name="Grid_CRoomsRecord" IsReadOnly="True" AutoGenerateColumns="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="时间" Width="120*" Binding="{Binding ExchangeDate,StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}"></DataGridTextColumn>
                <!--<DataGridTextColumn Header="" Width="50*" Binding="{Binding ExchangeDate}" ></DataGridTextColumn>-->
                <DataGridTextColumn Header="转出房号" Width="70*" Binding="{Binding FromRmNo}"></DataGridTextColumn>
                <DataGridTextColumn Header="转入房号" Width="70*" Binding="{Binding ToRmNo}" ></DataGridTextColumn>
                <DataGridTextColumn Header="经手人" Width="70*" Binding="{Binding UserName}" ></DataGridTextColumn>
            </DataGrid.Columns>
        </DataGrid>
        
    </Grid>
</UserControl>
