/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 17/07/2025 12:13:48
*/


-- ----------------------------
-- Table structure for RmType
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RmType]') AND type IN ('U'))
	DROP TABLE [dbo].[RmType]
GO

CREATE TABLE [dbo].[RmType] (
  [RtNo] varchar(2) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [RtName] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [MaxP] smallint DEFAULT 1 NOT NULL,
  [NoServ] bit DEFAULT 0 NOT NULL,
  [AccType] varchar(1) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT 'A' NOT NULL,
  [RmPrice] int DEFAULT 0 NOT NULL,
  [SRmPrice] int  NULL,
  [WeekEndPrice] int  NULL,
  [RealRoom] bit DEFAULT 1 NOT NULL,
  [CanAutoZD] bit DEFAULT 0 NOT NULL,
  [MaxZDRate] int DEFAULT 0 NOT NULL,
  [RmCostType] varchar(1) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT 0 NOT NULL,
  [ServRate] int DEFAULT 10 NOT NULL,
  [RmPrice_Person] int DEFAULT 100 NOT NULL,
  [SRmPrice_Person] int DEFAULT 100 NOT NULL,
  [WeekEndPrice_Person] int DEFAULT 100 NOT NULL,
  [RmPrice_PerUnit] int DEFAULT 100 NOT NULL,
  [SRmPrice_PerUnit] int DEFAULT 100 NOT NULL,
  [WeekEndPrice_PerUnit] int DEFAULT 100 NOT NULL,
  [UnitMinutes] int DEFAULT 60 NOT NULL,
  [MinMinutesOfTimeZone] int DEFAULT 15 NOT NULL,
  [MinMinutesOfTimeUnit] int DEFAULT 15 NOT NULL,
  [SetClearing] bit DEFAULT 0 NOT NULL,
  [rowguid] uniqueidentifier DEFAULT newid() NOT NULL ROWGUIDCOL,
  [msrepl_tran_version] uniqueidentifier DEFAULT newid() NOT NULL
)
GO

ALTER TABLE [dbo].[RmType] SET (LOCK_ESCALATION = TABLE)
GO


-- ----------------------------
-- Records of RmType
-- ----------------------------
INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'01', N'会所套', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'705309E0-BB79-4AB3-B2D3-B923F1ABE19F', N'BF3CF1EB-7DD4-4581-B47A-CB629B1D446D')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'02', N'总统套', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'2143C98A-715B-4A0C-9A9F-6D84435C25C1', N'D281138E-21FF-4290-A585-4B8B5DB83DF2')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'03', N'VIP套房', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'E8B0CB55-2A8A-48B7-B11A-3577BB85701A', N'5DBDCFF1-B279-4701-B749-************')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'04', N'PT大套房', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'BD314FB1-7D8A-48A2-BB3D-BD477769E80E', N'7CEA3E41-184F-4359-934D-7E404265F5B2')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'07', N'Party厅', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'E60A117F-319D-4EBF-B1FA-ED771C822560', N'4EB6B215-A453-4407-AB0C-8721C21FBCB3')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'06', N'中房', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'B59A002C-3F19-48B4-A38A-6743C372020B', N'B2C2635C-1C97-43B5-9042-0DCD8CCBAD77')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'08', N'花园VIP', N'1', N'0', N'A', N'4000', N'6000', N'4000', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'07314D9D-3D45-4A27-9F5D-4D1539131911', N'50FA486D-DAE1-487B-83A6-922F5AB1FB78')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'09', N'名堂厅', N'1', N'0', N'A', N'6000', N'20000', N'6000', N'1', N'1', N'0', N'0', N'3', N'500', N'500', N'500', N'100', N'100', N'100', N'60', N'15', N'15', N'0', N'D3B44C8F-BD07-4916-B921-F38F718511D6', N'5635EC92-9AB6-45CC-A397-7B7E48E2D994')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'19', N'花VIP-B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'0', N'E3008ADE-1AFB-4AE7-BC8F-DA85C02BC00C', N'2D8C901E-6FB8-4776-A227-CC422B24DF93')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'20', N'名堂厅-B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'0', N'59C553A2-E836-40E9-9F99-69072D4F6CAB', N'FB084192-3511-4CC4-B498-50BB970232E8')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'27', N'测试房', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'0', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'0', N'43341639-2CF1-4805-8F23-2E82AC3E7184', N'34CC8C0D-789A-401B-8ADF-B113CB93C361')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'12', N'会所套B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'8D977927-D14F-4AE1-9667-1FF635F90274', N'982F2089-B379-4693-83A8-A57DC5BDC812')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'13', N'总统套-B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'FE11C48E-5078-4B33-BA61-C20C7535F08B', N'FD132730-B904-4FF8-8362-C7BBB4E9BB66')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'14', N'VIP套-B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'6FBC2AD4-6945-4787-8A09-44BF2DDB9FE4', N'2CAA63EB-C63F-421A-BE92-1A3C2FD7E893')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'15', N'PT大套-B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'CF6F79EE-3B65-40FB-B911-4383A9376E8B', N'862230B5-73B7-4738-AC21-06FAEC39A144')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'31', N'会所房-C', N'1', N'0', N'A', N'6260', N'6260', N'6260', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'A9A835C1-F2BF-41F5-B904-B964AF750176', N'9AEE1526-CC28-4E1C-9F3B-F54F35A8D013')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'17', N'中房B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'547AC64B-015C-446C-87EC-F5659400D16C', N'760E0D50-FEF8-41D1-AD7A-17D9551A1D1F')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'32', N'总统房-C', N'1', N'0', N'A', N'4660', N'4660', N'4660', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'18F81A89-4F37-48AF-9DEB-880C6BA5EB9B', N'19F7C296-6165-4F01-B337-F8882B5C16D0')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'33', N'VIP房-C', N'1', N'0', N'A', N'3360', N'3360', N'3360', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'A483B698-D83A-4247-A126-D9B26D025DD9', N'4A6EF598-D5C8-4022-9F2B-DFB29BB68D60')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'34', N'PT大房-C', N'1', N'0', N'A', N'2560', N'2560', N'2560', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'4CBB55B8-777C-4A9F-9E17-EC9738A3E24B', N'F7FDD47A-806E-4DB6-9EDC-12A2F58C2A25')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'35', N'PT中房-C', N'1', N'0', N'A', N'1960', N'1960', N'1960', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'AADD2A79-2409-470E-908A-16DE35F6B7B0', N'84D282C9-EA5D-440D-A67D-F12433839E06')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'36', N'Party厅-C', N'1', N'0', N'A', N'8260', N'8260', N'8260', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'20B1E209-7809-4BE0-A180-AD4F429F0312', N'93052E9A-AA97-4EE6-9547-B274438B7A22')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'23', N'备用房', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'0', N'15576BB4-C48F-43ED-983A-3AED03ADD8F5', N'4C1C101D-470E-412E-AFDA-784E2C6C3BCE')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'21', N'沙壶VIP', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'AB3E9144-A887-4739-925B-16E4ED14DAD9', N'925A4038-A89E-4E0D-9BA8-1DD215988FE3')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'22', N'沙壶-B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'1', N'714C2DF8-9234-4E7F-9887-78146C331473', N'068F1CA8-5D84-4D4E-A686-48665F2654BB')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'26', N'中转房', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'0', N'0', N'1', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'0', N'67CFCFD2-7E5E-45A7-B9B9-54F952800E16', N'59A3130A-F4A7-44D2-938E-A3E695874751')
GO

INSERT INTO [dbo].[RmType] ([RtNo], [RtName], [MaxP], [NoServ], [AccType], [RmPrice], [SRmPrice], [WeekEndPrice], [RealRoom], [CanAutoZD], [MaxZDRate], [RmCostType], [ServRate], [RmPrice_Person], [SRmPrice_Person], [WeekEndPrice_Person], [RmPrice_PerUnit], [SRmPrice_PerUnit], [WeekEndPrice_PerUnit], [UnitMinutes], [MinMinutesOfTimeZone], [MinMinutesOfTimeUnit], [SetClearing], [rowguid], [msrepl_tran_version]) VALUES (N'18', N'Party厅B', N'1', N'0', N'A', N'0', N'0', N'0', N'1', N'1', N'0', N'0', N'3', N'100', N'100', N'100', N'100', N'100', N'100', N'60', N'15', N'15', N'0', N'8B275AFE-1C4E-45E8-BD13-CE52D3DE4183', N'34BBA86A-516D-49AE-8811-4B2DA1903E2B')
GO


-- ----------------------------
-- Indexes structure for table RmType
-- ----------------------------
CREATE UNIQUE NONCLUSTERED INDEX [index_324196205]
ON [dbo].[RmType] (
  [rowguid] ASC
)
GO


-- ----------------------------
-- Primary Key structure for table RmType
-- ----------------------------
ALTER TABLE [dbo].[RmType] ADD CONSTRAINT [PK_RmType] PRIMARY KEY NONCLUSTERED ([RtNo])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

