﻿using ComponentWpfInterface;
using ComponentWpfInterface.InterfaceAttribute;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RMS2018.ComponentWpf
{
    [PurposeAttribute(Title = "mystest-Test03", GroupName = "mytest_test03")]
    public class WpfPageConfig : IWpfPageConfig
    {
        public ComponentWpfInterface.Model.PageInfo GetPage()
        {
            return new ComponentWpfInterface.Model.PageInfo()
            {
                Title = "前台系统",
                ICO = "\uf00a",
                Page = new MainPage()


            };
        }
    }
}
