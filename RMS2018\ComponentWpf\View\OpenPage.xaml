﻿<UserControl x:Class="RMS2018.ComponentWpf.OpenPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:RMS2018.Controls"
             mc:Ignorable="d" 
               xmlns:local="clr-namespace:RMS2018"
             d:DesignHeight="300" d:DesignWidth="300">
    <UserControl.Resources>
        <ResourceDictionary>
            <Style TargetType="TextBlock"  x:Key="tbTitleStyle"  >
                <Setter Property="Margin" Value="10,0,8,0"></Setter>

                <Setter Property="VerticalAlignment" Value="Center"></Setter>
                <Setter Property="FontSize" Value="10" ></Setter>
                <Setter Property="Foreground" Value="#FF898989"></Setter>
            </Style>
            <Style TargetType="TextBlock" x:Key="tbConent"  >
                <Setter Property="VerticalAlignment" Value="Center"></Setter>
                <Setter Property="Margin" Value="13,0,0,0" ></Setter>
            </Style>
            <Style TargetType="TextBox"  >
                <Setter Property="VerticalContentAlignment" Value="Center"></Setter>

            </Style>
            <Style TargetType="Button" x:Key="BtnFun"  >
                <Setter Property="Margin" Value="5,0"></Setter>
                <Setter Property="Padding" Value="8"></Setter>
            </Style>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/Dictionary1.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="35"></RowDefinition>
                <RowDefinition Height="255"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid Grid.Row="0" Background="#3d3d3d">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="75*"></ColumnDefinition>
                    <ColumnDefinition Width="25*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <StackPanel Orientation="Horizontal" Name="st">
                    <Controls:UTitleInfo/>
                    <Controls:USMSInfo x:Name="usmsinfo" MouseLeftButtonUp="usmsinfo_MouseLeftButtonUp"  />

                </StackPanel>
                <StackPanel Orientation="Horizontal" Grid.Column="1" HorizontalAlignment="Right" >

                    <Button Margin="5"  Width="80" Name="btnExtend" Click="btnExtend_Click"  >扩展平台</Button>
                    <CheckBox VerticalAlignment="Center"  Name="isDbfoodSynchro"  Foreground="White" Margin="5,0" Click="isDbfoodSynchro_Click" Visibility="Collapsed" >天王同步</CheckBox>

                    <Button Margin="5"  Width="50" Name="btnBook" Click="btnBook_Click">订房</Button>

                </StackPanel>
            </Grid>
            <Grid Margin="0,0,0,5"  Grid.Row="1" Background="#323234">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="280"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition Width="355"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Grid >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"></RowDefinition>
                        <RowDefinition Height="15"></RowDefinition>
                    </Grid.RowDefinitions>
                    <DataGrid   LoadingRow="dgBook_LoadingRow"   MouseLeftButtonUp="dgBook_MouseLeftButtonDown"   Name="dgBook"  IsReadOnly="True" AutoGenerateColumns="False"  >
                        <DataGrid.Columns>
                            <DataGridTextColumn  Header="预约号" Width="45"  Binding="{Binding BookNo}"/>
                            <DataGridTextColumn  Header="姓名" Width="50"  Binding="{Binding CustName}"/>
                            <DataGridTextColumn  Header="电话" Width="85"  Binding="{Binding CustTel}"/>
                            <DataGridTextColumn  Header="人数" Width="33"  Binding="{Binding Numbers}"/>
                            <DataGridTextColumn  Header="订金" Width="*"   Binding="{Binding DepositTotString}"/>
                        </DataGrid.Columns>
                    </DataGrid>
                    <Grid  Grid.Row="1" Background="White" VerticalAlignment="Center">
                        <Grid Background="#FFF5F1F1"  Margin="0,0,5,0"  HorizontalAlignment="Right" >

                            <Viewbox  VerticalAlignment="Center" ToolTip="预约记录刷新" Margin="35,0,0,0" >
                                <Image Margin="20"  Name="imgClear_ico"  MouseUp="imgRefresh_MouseUp"  Source="pack://siteoforigin:,,,/Resources/Refresh.png"  ></Image>
                            </Viewbox>
                            <Viewbox  VerticalAlignment="Center" ToolTip="填写内容清空"  Margin="0,0,0,0">
                                <Image Margin="20"  Name="imgClear" Source="pack://siteoforigin:,,,/Resources/clear.png"  MouseUp="imgClear_MouseUp"></Image>
                            </Viewbox>
                        </Grid>
                        <StackPanel Orientation="Horizontal" >
                            <TextBlock Margin="0,0" Foreground="#777">总数：</TextBlock>
                            <TextBlock Margin="0" Name="tbBookTot">0</TextBlock>
                            <TextBlock Margin="5,0,0,0"  Foreground="#777">超时：</TextBlock>
                            <TextBlock Margin="0" Name="tbBookOut">0</TextBlock>
                            <TextBlock Margin="5,0,0,0"  Foreground="#777">折扣：</TextBlock>
                            <TextBlock Margin="0" Name="tbBookDiscount">0</TextBlock>
                            <TextBlock Margin="5,0,0,0"  Foreground="#777">订金：</TextBlock>
                            <TextBlock Margin="0" Name="tbDeposit">0</TextBlock>
                            <TextBlock Margin="8,0,0,0"  Foreground="#777" Visibility="Collapsed">预下单：</TextBlock>
                            <TextBlock Margin="0" Name="tbPreorder" Visibility="Collapsed">0</TextBlock>
                        </StackPanel>
                    </Grid>
                </Grid>
                <StackPanel Grid.Column="1" >
                    <Grid  Background="#FFF6F3F3"  Margin="0"  Height="45" >
                        <StackPanel  Orientation="Horizontal" Margin="10,3">
                            <TextBlock   Style="{DynamicResource tbTitleStyle}"><Run Text="日期"/>
                            </TextBlock>
                            <TextBlock FontSize="14" Width="120" Margin="0" VerticalAlignment="Center" Text="{Binding vmpc.vmTimeDate.worddDateString}"/>
                            <TextBlock  Style="{DynamicResource tbTitleStyle}" >
                <Run Text="时段"/>
                            </TextBlock>
                            <!--<TextBox IsEnabled="False" Name="tbShopTime"></TextBox>
                        <Button>：</Button>-->
                            <ComboBox IsEnabled="False" SelectionChanged="tbShopTime_SelectionChanged" SelectedValuePath="TimeNo" Height="30" DisplayMemberPath="TimeName" Width="150" x:Name="tbShopTime" FontSize="18"/>
                            <TextBlock  Style="{DynamicResource tbTitleStyle}">
                <Run Text="查询"/>
                            </TextBlock>
                            <Controls:HdTextBox Width="150"   x:Name="tbSearch" KeyUp="tbSearch_KeyDown" Tis="电话/预约号/性别/姓氏"  />



                            <TextBlock  Foreground="Red" x:Name="searchTitle"/>
                        </StackPanel>
                    </Grid>
                    <Grid Margin="0,2,0,0" Background="#FFF6F3F3">
                        <StackPanel Background="#FFF6F3F3"  Margin="10,10"   Height="135" >
                            <StackPanel   Orientation="Horizontal" Height="35" Margin="0,5" >
                                <TextBlock Style="{DynamicResource tbTitleStyle}" ><Run Text="姓名"/>
                                </TextBlock>
                                <TextBox Width="80" Height="30" x:Name="tbName" FontSize="15"/>
                                <Controls:URadioButton   x:Name="rbSex1" IsChecked="True" Foreground="#7c7c7f"  Style="{DynamicResource BoxRadioButton}" Content="先生" FontSize="16" />
                                <Controls:URadioButton   x:Name="rbSex2" Foreground="#7c7c7f"  Style="{DynamicResource BoxRadioButton}" Content="女士" FontSize="16" />
                                <TextBlock Style="{DynamicResource tbTitleStyle}" ><Run Text="电话"/>
                                </TextBlock>
                                <!--<TextBox Name="tbTel" MaxLength="11" Width="110" TextChanged="tbTel_TextChanged"  Style="{DynamicResource TextBoxLoading}"></TextBox>-->
                                <Controls:UCust_Tel  Height="30" Width="180"  x:Name="tbTel" TextChanged="tbTel_TextChanged_1" FontSize="15" />
                                <CheckBox VerticalContentAlignment="Center" x:Name="cbDiscount" Margin="20,0,0,0" Content="特别打折"  FontSize="12"  Style="{DynamicResource Checkbox_yes}"/>
                                <!--<Viewbox Margin="5">
                                <Image Source="pack://siteoforigin:,,,/Resources/discount.png"/>
                            </Viewbox>-->
                                <Controls:UWeChatSearch   VerticalContentAlignment="Center" Width="130"  Margin="20,0,0,0" x:Name="uwechatSearch" FontSize="15" />

                            </StackPanel>
                            <Rectangle Height="1" Fill="#FFE0E0E0" Margin="10,0"></Rectangle>

                            <StackPanel Orientation="Horizontal"  Height="35" Margin="0,5" >

                                <TextBlock Style="{DynamicResource tbTitleStyle}" > 房号</TextBlock>
                                <TextBlock Margin="0" Width="60" x:Name="tbRmno" FontSize="15"  VerticalAlignment="Center"/>
                                <TextBlock  Style="{DynamicResource tbTitleStyle}"><Run Text="房型"/>
                                </TextBlock>
                                <TextBlock Margin="0" Width="100" x:Name="tbRtName" FontSize="15" VerticalAlignment="Center" />
                                <TextBlock Style="{DynamicResource tbTitleStyle}"><Run Text="当房" />
                                </TextBlock>
                                <ComboBox  SelectedValuePath="RtNo"  VerticalContentAlignment="Center"   Height="30" SelectionChanged="cbRoomType_SelectionChanged"  DisplayMemberPath="RtName"  Width="100" x:Name="cbRoomType"/>
                                <TextBlock Style="{DynamicResource tbTitleStyle}"> 预订房型</TextBlock>
                                <TextBlock Margin="0" Width="100"  FontWeight="Bold" Foreground="#FFC51010" x:Name="tbBookRt" FontSize="16" VerticalAlignment="Center"/>
                                <TextBlock Style="{DynamicResource tbTitleStyle}"> 预约到达</TextBlock>
                                <TextBlock Margin="0" Width="100" x:Name="tbEnd_Time" FontSize="15" VerticalAlignment="Center"/>
                                <TextBlock Style="{DynamicResource tbTitleStyle}"> 结束时段</TextBlock>
                                <TextBlock Margin="0" Width="100" x:Name="tbEnd_Name" FontSize="15" VerticalAlignment="Center"/>

                            </StackPanel>

                            <Rectangle Height="1" Fill="#FFE0E0E0" Margin="10,0"></Rectangle>
                            <StackPanel  Orientation="Horizontal" Background="White" Margin="0,5"  Height="35" >
                                <TextBlock Style="{DynamicResource tbTitleStyle}" >到达</TextBlock>
                                <Controls:DateTimePicker FontSize="15" Width="135" x:Name="dtpComtime"/>
                                <TextBlock Style="{DynamicResource tbTitleStyle}"><Run Text="消费类型"/>
                                </TextBlock>
                                <ComboBox Width="100" VerticalContentAlignment="Center"   Height="30" SelectedValuePath="CtNo"  DisplayMemberPath="CtName"  x:Name="cbConType" FontSize="16"/>
                                <TextBlock  Style="{DynamicResource tbTitleStyle}"><Run Text="人数"/>
                                </TextBlock>
                                <Controls:NumbiricTextBox TextAlignment="Center" FontSize="15" x:Name="bookNumber" VerticalContentAlignment="Center" Width="40"/>
                                <TextBlock  Style="{DynamicResource tbTitleStyle}"><Run Text="订房留言"  />
                                </TextBlock>
                                <TextBox  TextWrapping="Wrap" x:Name="tbBookMemory" Width="150"/>
                                <TextBlock Style="{DynamicResource tbTitleStyle}" ><Run Text="开房备注"/>
                                </TextBlock>
                                <TextBox  TextWrapping="Wrap" Width="150" x:Name="tbOpenMemory"/>
                            </StackPanel>



                            <StackPanel  Orientation="Horizontal" Background="White" Margin="0,0,0,1"  Height="35">


                            </StackPanel>
                        </StackPanel>
                    </Grid>



                    <Grid Background="White" Margin="0,2"    Height="45">
                        <StackPanel  Orientation="Horizontal" Margin="0,2" >
                            <Button  Style="{StaticResource BtnFunction}"  IsEnabled="False"  x:Name="btnOpen" Click="btnOpen_Click" Content="开房" Background="#FFFBB5B6"/>
                            <Button  Style="{StaticResource BtnFunction}"  IsEnabled="False"  x:Name="btnRoomTo" Click="btnRoomTo_Click" Content="派房" Background="#FFF7F792"/>
                            <Button  Style="{StaticResource BtnFun}" Visibility="Collapsed"  x:Name="btnRefresh" Click="btnRefresh_Click" Content="刷新"/>
                            <Button  Style="{StaticResource BtnFun}"  Visibility="Collapsed" Content="同步"/>
                            <Button  Style="{StaticResource BtnFun}"  Visibility="Collapsed" x:Name="btnRoomToRefresh" IsEnabled="False" Click="btnRoomToRefresh_Click" Content="重新派房"/>
                            <Button  Style="{StaticResource BtnFunction}"  x:Name="btnAllOpen" Click="btnAllOpen_Click" Content="全部开房"/>
                            <Button  Style="{StaticResource BtnFunction}"  x:Name="btnCustBehaInfo" Click="btnCustBehaInfo_Click">
                                <Grid>
                                    <TextBlock Margin="0">
                    <Run Text="顾客行为"/>
                                    </TextBlock>
                                    <Border  Height="15" Width="15" CornerRadius="15" Background="{Binding vmpc.vmCustInfo.mCustInfoCall.BechaNumberColor}"   VerticalAlignment="Top" Margin="0,-11,-9,0"  HorizontalAlignment="Right">
                                        <Viewbox Margin="0" >
                                            <TextBlock  Margin="0" Padding="0" Foreground="White"  Text="{Binding vmpc.vmCustInfo.mCustInfoCall.BechaNumber}"/>
                                        </Viewbox>
                                    </Border>

                                </Grid>
                            </Button>

                            <Button  Style="{StaticResource BtnFunction}" x:Name="btnSms" Click="btnSms_Click">

                                <Grid>
                                    <TextBlock Margin="0">
                    <Run Text="内部消息"/>
                                    </TextBlock>
                                    <Ellipse  Height="10" Width="10" Fill="Red" Visibility="Collapsed"  x:Name="btnMsgEllipse" VerticalAlignment="Top" Margin="0,-5,-7,0" HorizontalAlignment="Right"/>
                                </Grid>
                            </Button>
                            <Button Style="{StaticResource BtnFun}"  Visibility="Collapsed" Click="btnPrintFormat_Click" x:Name="btnPrintFormat" Content="自定义小票"/>

                            <!--<Button Style="{StaticResource BtnFunction}" Click="BtnbusinessReport_Click" x:Name="BtnbusinessReport" Content="营业报表">

                            </Button>
                            <Button Style="{StaticResource BtnFunction}" Click="BtnDataHistory_Click" x:Name="BtnDataHistory" Content="资料查询" >

                            </Button>
                            <Button Style="{StaticResource BtnFunction}" Click="BtnDeposit_Click" x:Name="BtnDeposit" Content="微信订金" >

                            </Button>-->
                            <!--<Button Style="{StaticResource BtnFunction}" Click="BtnWechatRoom_Click" x:Name="BtnWechatRoom" Content="微信取房" >
                               
                            </Button>-->
                            <Button Visibility="Collapsed" Style="{StaticResource BtnFun}" Click="btnAllClear_Click" x:Name="btnAllClear" Content="全部清房" />
                            <Button  Visibility="Collapsed" Style="{StaticResource BtnFun}" Click="btnClear_Click" x:Name="btnClear" Content="单间清房" />
                            <!--<Button Style="{StaticResource BtnFunction}"  x:Name="btnRoomStatus" Click="btnRoomStatus_Click" Content="房间状态">
                              
                            </Button>-->
                            <!--<Button Style="{StaticResource BtnFunction}" x:Name="btnCloseRoom" Click="btnCloseRoom_Click" Content="关房管理">

                            </Button>-->
                            <!--2020-11-09 jjy添加-->
                            <!--<Button Style="{StaticResource BtnFunction}"  x:Name="btnPreorder" Content="预下单" Click="btnPreorder_Click"/>
                            <Button Style="{StaticResource BtnFunction}"  x:Name="btnbirthday_giftPay" Click="btnbirthday_giftPay_Click_1" Content="生日定金">

                            </Button>
                            <Button Style="{StaticResource BtnFunction}"  x:Name="btnReset" Click="btnReset_Click" Content="重置锁定">

                            </Button>-->
                            <Grid  MouseEnter="btnMore_MouseEnter"    >
                                <Button Style="{StaticResource BtnFunction}"  x:Name="btnMore" Click="btnMore_Click" Content="更多..."  ToolTipService.ShowDuration="700000" ToolTipService.BetweenShowDelay="70000" >
                                    <!--<Button.ToolTip>
                                        <Grid Background="Black" Width="100" Height="100"></Grid>

                                    </Button.ToolTip>-->
                                </Button>
                                <Popup  PopupAnimation="Scroll"    PlacementTarget="{Binding ElementName=btnMore}" AllowsTransparency="True"   Name="popupFunction" Placement="Right" StaysOpen="False"  >
                                    <Controls:UMoreFunction   MouseLeave="btnMore_MouseLeave"  />
                                </Popup>
                            </Grid>

                            
                            <!--<CheckBox x:Name="cbPrint" ToolTip="小票打印开关设置" Margin="5"  Style="{DynamicResource BoxRadio_Print}"   Click="cbPrint_Click" Content="小票打印"  VerticalAlignment="Center"  IsChecked="True" />-->

                            <TextBlock x:Name="dbfoodmsg"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>


                <TabControl Grid.Column="2" Name="tabCustInfo" >
                    <TabItem Header="" Name="tabItem01" IsEnabled="False"></TabItem>
                    <TabItem Header="顾客信息"  Selector.IsSelected="True">
                        <StackPanel>
                            <Controls:OpenRoomInfo  x:Name="openRoom" />
                            <Grid>
                                <Rectangle Fill="#FFE6E7E9" Height="2" Margin="38,0,0,0"></Rectangle>
                                <TextBlock  Text="订房信息" Width="38"  HorizontalAlignment="Left"  FontSize="9" Margin="0" Padding="0,0,0,0"  FontWeight="Bold" Foreground="#FF9E9E30"></TextBlock>
                            </Grid>
                            <Controls:OrderRoomInfo x:Name="bookRoom" />
                        </StackPanel>
                    </TabItem>
                    <TabItem Header="会员信息">
                        <Controls:UMemberInfo  x:Name="umemberinfo"/>
                    </TabItem>
                </TabControl>
                <!--<StackPanel Grid.Column="2" VerticalAlignment="Top" Margin="2,0" HorizontalAlignment="Right"  Orientation="Horizontal">

                    <Label   Foreground="White"  Background="#FF125271"  Height="30" >001</Label>
                </StackPanel>-->

            </Grid>
            <Grid Grid.Row="2">
                <Controls:UShop  x:Name="uRoom"  />
            </Grid>


        </Grid>
        <Grid Background="Black" Opacity="0.95" Name="gridCustBehaInfo"  MouseLeftButtonUp="gridCustBehaInfo_MouseLeftButtonUp"  Visibility="{Binding  ElementName=uCustBehaInfo,Path=Visibility}" >
            <Controls:UCustBehaInfo Width="1000" Height="750" x:Name="uCustBehaInfo"  Visibility="Collapsed"/>
        </Grid>


        <!--<Grid Background="Black"    Visibility="{Binding  ElementName=uCanvasPrintFormat,Path=Visibility}" >
            <Controls:UserControlCanvas x:Name="uCanvasPrintFormat"  Visibility="Collapsed"/>
        </Grid>-->


        <Grid Background="Black"   Visibility="{Binding  ElementName=roomchange,Path=Visibility}"   >
            <Controls:RoomChange  Background="White" x:Name="roomchange"  Visibility="Collapsed"/>

        </Grid>
        <Grid Background="Black"  Name="gridBad" Visibility="Collapsed" >
            <StackPanel Orientation="Horizontal" Height="50" HorizontalAlignment="Center">
                <TextBlock Foreground="White">坏房原因：</TextBlock>
                <TextBox Width="500" Name="tbBadText"></TextBox>
                <Button Width="50" Margin="0,0,0,0" Name="btnOk" Click="btnOk_Click">确定</Button>
                <Button  Width="50" Margin="5,0" Name="BtnNo" Click="BtnNo_Click">取消</Button>
            </StackPanel>
        </Grid>
        <Grid   Opacity="0.97" x:Name="gridbackstage"  Background="Black"  Visibility="{Binding Visibility, ElementName=updcust}">
            <Controls:UpdOpenCacheInfo  x:Name="updcust" Visibility="Collapsed"/>

        </Grid>
        <Grid  Background="Black"   Opacity="0.95" Visibility="{Binding Visibility, ElementName=addtims}">
            <Controls:AddTimes x:Name="addtims"  Visibility="Collapsed" />

        </Grid>
        <!--<Controls:ReFillOpenData />-->
        <!--2020-12-21 jjy 添加，手动回绑开房数据-->
        <Grid   Opacity="0.97" Background="Black" Visibility="{Binding Visibility, ElementName=refill}">
            <Controls:ReFillOpenData x:Name="refill" Visibility="Collapsed" />

        </Grid>

        <Grid Name="loading"    Visibility="Collapsed" >
            <Grid Background="Black" Opacity="0.5"></Grid>
            <StackPanel  HorizontalAlignment="Center" VerticalAlignment="Center">
                <Controls:LoadingWait  Background="Transparent" />
                <TextBlock Margin="0,20,0,0" Foreground="White">系统正在处理中，请稍候（<Run Name="loadingTotal">0</Run>/<Run Name="loadingSurplus">0</Run>）</TextBlock>

                <Button Margin="10" Click="btnTipClose">关闭</Button>
            </StackPanel>
        </Grid>


    </Grid>
</UserControl>
