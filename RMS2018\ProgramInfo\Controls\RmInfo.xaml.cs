using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using RMSBLL.RMS;
using RMSModel.Config;
using RMSModel.ExtensionRMS;
using RMSModel.RMS;
namespace RMS2018.ProgramInfo.Controls
{
    /// <summary>
    /// RmInfo.xaml 的交互逻辑
    /// </summary>
    public partial class RmInfo : UserControl
    {
        /// <summary>
        /// 所有房间信息对象集合
        /// </summary>
        List<MRm_Rt_MArea_MShop> RmInfos;
        /// <summary>
        /// 初始化所有房间信息对象集合
        /// </summary>
        List<MRm_Rt_MArea_MShop> IniRmInfos;
        /// <summary>
        /// 初始化门店集合
        /// </summary>
        List<MShopInfo> IniShopInfos;
        /// <summary>
        /// 初始化房型信息
        /// </summary>
        List<MRt_MShop> IniRtInfos;
        /// <summary>
        /// 初始化区域信息
        /// </summary>
        List<MArea_MShop> IniAreaInfos;
        /// <summary>
        /// 所有勾选的主键集合
        /// </summary>
        List<MRm_Rt_MArea_MShop> ReMoveInfo = new List<MRm_Rt_MArea_MShop>();
        //操作员工
        string Username = "";
        //员工编号
        string UserId = "";
        string oldRmNo = "";
        string oldShopId;
        private int ShopId;

        public RmInfo(MUserInfo Mu)
        {
            InitializeComponent();
            try
            {
                this.ShopId = CFixedConfig.shopInfoStatic.ShopId;
                UserId = Mu.UserId;
                Username = Mu.UserName;
                //this.ShopId = 2;
                RmInfos = IniRmInfos = MRmInfoBll.GetMRmInfo(ShopId);
                IniShopInfos = MShopInfoBll.GShopInfoAll().GroupBy(r => r.ShopName).Select(r => r.First()).ToList();
                IniRtInfos = MRtInfoBll.GRtInfoAll(ShopId).ToList();
                IniAreaInfos = MAreaInfoBll.GetMAreaInfo(ShopId).ToList();
                SetData();
            }
            catch
            {

            }
        }

        /// <summary>
        /// 给界面的筛选框以及数据表添加数据
        /// </summary>
        private void SetData()
        {
            if (ShopId == 1)
            {
                Com_ShopNames.Visibility = Visibility.Visible;
                List<MShopInfo> ShopInfos = IniShopInfos.ToList();
                ShopInfos.Insert(0, new MShopInfo() { ShopName = "所有门店（全部）", ShopId = 1 });
                Com_ShopNames.ItemsSource = ShopInfos;
                Com_ShopNames.SelectedIndex = 0;
                Com_Shops.ItemsSource = IniShopInfos;
            }

            Com_RtNames.ItemsSource = IniRtInfos;
            Com_AreaNames.ItemsSource = IniAreaInfos;
            Dg_RmsInfo.ItemsSource = RmInfos;

            List<MRt_MShop> RtInfos = IniRtInfos.ToList();
            RtInfos.Insert(0, new MRt_MShop() { RtName = "全部房型" });
            Com_RtName.ItemsSource = RtInfos;
            Com_RtName.SelectedIndex = 0;

            List<MArea_MShop> AreaInfos = IniAreaInfos.ToList();
            AreaInfos.Insert(0, new MArea_MShop() { AreaName = "所有区域" });
            Com_AreaName.ItemsSource = AreaInfos;
            Com_AreaName.SelectedIndex = 0;
        }


        /// <summary>
        /// 根据七或八个条件筛选框选项进行筛选
        /// </summary>
        public void RmInfoByAlls()
        {
            try
            {
                RmInfos = IniRmInfos;
                if (ShopId == 1)
                {
                    if (Com_ShopNames != null && Com_ShopNames.SelectedItem != null)
                    {
                        if ((Com_ShopNames.SelectedItem as MShopInfo).ShopId != 1)
                        {
                            RmInfos = RmInfos.FindAll(i => i.ShopName == ((Com_ShopNames.SelectedItem as MShopInfo).ShopName));
                        }
                    }
                }
                if (Com_RtName != null && Com_RtName.Text != "")
                {
                    if ((Com_RtName.SelectedItem as MRt_MShop).RtName != "全部房型")
                    {
                        RmInfos = RmInfos.Where(i => i.RtName == (Com_RtName.SelectedItem as MRt_MShop).RtName).ToList();
                    }
                }
                if (Com_AreaName != null && Com_AreaName.Text != "")
                {
                    if ((Com_AreaName.SelectedItem as MArea_MShop).AreaName != "所有区域")
                    {
                        RmInfos = RmInfos.Where(i => i.AreaName == (Com_AreaName.SelectedItem as MArea_MShop).AreaName).ToList();
                    }
                }

                if (Txt_RmNos != null && Txt_RmNos.Text != "")
                {
                    RmInfos = RmInfos.Where(i => i.RmNo.IndexOf(Txt_RmNos.Text) >= 0).ToList();
                }
                if (Txt_MealAreaSorts != null && Txt_MealAreaSorts.Text != "")
                {
                    RmInfos = RmInfos.Where(i => i.MealAreaSort == int.Parse(Txt_MealAreaSorts.Text)).ToList();
                }
                if (Txt_FloorNumbers != null && Txt_FloorNumbers.Text != "")
                {
                    RmInfos = RmInfos.Where(i => i.FloorNumber == int.Parse(Txt_FloorNumbers.Text)).ToList();
                }
                if (Com_IsDisplay != null && Com_IsDisplay.SelectedItem != null)
                {
                    if ((Com_IsDisplay.SelectedItem as ComboBoxItem).Tag.ToString() == "0")
                    {
                        RmInfos = RmInfos.FindAll(i => i.IsDisplay == true).ToList();
                    }
                    if ((Com_IsDisplay.SelectedItem as ComboBoxItem).Tag.ToString() == "1")
                    {
                        RmInfos = RmInfos.FindAll(i => i.IsDisplay == false).ToList();
                    }
                }
                if (Com_IsExistWc != null && Com_IsExistWc.SelectedItem != null)
                {
                    if ((Com_IsExistWc.SelectedItem as ComboBoxItem).Tag.ToString() == "0")
                    {
                        RmInfos = RmInfos.FindAll(i => i.IsExistWc == true).ToList();
                    }
                    if ((Com_IsExistWc.SelectedItem as ComboBoxItem).Tag.ToString() == "1")
                    {
                        RmInfos = RmInfos.FindAll(i => i.IsExistWc == false).ToList();
                    }
                }
                if (Dg_RmsInfo != null)
                {
                    Dg_RmsInfo.ItemsSource = RmInfos;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 文本变更事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Txt_RmNos_TextChanged(object sender, TextChangedEventArgs e)
        {
            RmInfoByAlls();
            e.Handled = true;
        }

        /// <summary>
        /// 门店下拉框变更事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Com_ShopNames_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            RmInfoByAlls();
            e.Handled = true;
        }

        /// <summary>
        /// 批量删除按钮（已屏蔽，暂停用）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_RemoveRmMore_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ReMoveInfo.Count > 0)
                {
                    MessageBoxResult dr = MessageBox.Show("是否删除所勾选房型?", "提示", MessageBoxButton.OKCancel, MessageBoxImage.Question);
                    if (dr == MessageBoxResult.OK)
                    {
                        string RmIkeys = "";
                        foreach (var item in ReMoveInfo)
                        {
                            RmIkeys += item.RmNo + "-" + item.ShopId + ",";
                        }
                        MRmInfoBll.GetMRmInfo_Del(RmIkeys);
                        ReMoveInfo.Clear();
                        IniRmInfos = MRmInfoBll.GetMRmInfo(ShopId);
                        RmInfoByAlls();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 添加按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_AddRm_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Grid_All.Opacity = 0.7;
                Grid_AddUpd.DataContext = null;
                Grid_AddUpd.Visibility = Visibility.Visible;
                if (ShopId != 1)
                {
                    Com_Shops.ItemsSource = IniShopInfos.FindAll(i => i.ShopId == ShopId).ToList();
                }
                if (Com_Shops.Items.Count > 0)
                {
                    Com_Shops.SelectedIndex = 0;
                }
                if (Com_RtNames.Items.Count > 0)
                {
                    Com_RtNames.SelectedIndex = 0;
                }
                if (Com_AreaNames.Items.Count > 0)
                {
                    Com_AreaNames.SelectedIndex = 0;
                }
                Txt_MealAreaSort.Value = 0;
                Txt_FloorNumber.Value = 0;
                Btn_Add.Content = "确认添加";
                Btn_Add.Tag = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_RemoveRm_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBoxResult dr = MessageBox.Show("是否删除当前项?", "提示", MessageBoxButton.OKCancel, MessageBoxImage.Question);
                if (dr == MessageBoxResult.OK)
                {
                    MRm_Rt_MArea_MShop Mrp = (sender as Button).Tag as MRm_Rt_MArea_MShop;
                    MRmInfo mr = new MRmInfo();
                    mr.RmNo = Mrp.RmNo;
                    mr.ShopId = Mrp.ShopId;
                    int count = MRmInfoBll.GetMRmInfo_Del(mr);
                    if (count > 0)
                    {
                        OperationLog OpLog = new OperationLog() { UserId = UserId, UserName = Username, TableName = "RmInfo（房间信息表）", types = "2", Conent = Mrp.RmNo };
                        OperationLogBll.OperationLogAdd(OpLog);
                        MessageBox.Show("删除成功");
                        IniRmInfos = MRmInfoBll.GetMRmInfo(ShopId);
                        RmInfoByAlls();
                    }
                    else
                    {
                        MessageBox.Show("删除失败");
                    }
                }
            }
            catch { }
        }

        /// <summary>
        /// 编辑按钮点击事件 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_UpdRm_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Grid_AddUpd.Visibility = Visibility.Visible;
                Grid_AddUpd.DataContext = null;
                if (ShopId != 1)
                {
                    Com_Shops.ItemsSource = IniShopInfos.FindAll(i => i.ShopId == ShopId).ToList();
                }
                if (Com_Shops.Items.Count > 0)
                {
                    Com_Shops.SelectedIndex = 0;
                }
                MRm_Rt_MArea_MShop Mtp = (sender as Button).Tag as MRm_Rt_MArea_MShop;
                Grid_AddUpd.DataContext = Mtp;
                oldRmNo = Mtp.RmNo;
                oldShopId = Mtp.ShopId.ToString();
                if (Mtp.IsExistWc == true)
                {
                    Ck_IsExistWc.IsChecked = true;
                }
                if (Mtp.IsDisplay == false)
                {
                    Ck_IsDisplay.IsChecked = true;
                }
                Btn_Add.Content = "确认编辑";
                Btn_Add.Tag = 1;
                Grid_All.Opacity = 0.7;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 批量删除的勾选单选框点击事件（已屏蔽，暂停用）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Check_RemoveRm_Click(object sender, RoutedEventArgs e)
        {

            if ((sender as CheckBox).IsChecked == true)
            {
                ReMoveInfo.Add((sender as CheckBox).Tag as MRm_Rt_MArea_MShop);
            }
            else
            {
                ReMoveInfo.Remove((sender as CheckBox).Tag as MRm_Rt_MArea_MShop);
            }
        }

        /// <summary>
        /// 右边添加界面的文本变更事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Txt_RmNo_TextChanged(object sender, TextChangedEventArgs e)
        {
            Txt_Option.Text = "";
            Txt_Option.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 右边添加界面的文本键盘按下事件，（限定整数数字）
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Txt_RmNo_KeyDown(object sender, KeyEventArgs e)
        {
            var txt = sender as TextBox;
            //屏蔽非法按键
            if (e.Key >= Key.NumPad0 && e.Key <= Key.NumPad9)
            {
                e.Handled = false;
            }
            else if ((e.Key >= Key.D0 && e.Key <= Key.D9) && e.KeyboardDevice.Modifiers != ModifierKeys.Shift)
            {
                e.Handled = false;
            }
            else if (e.Key == Key.Enter)
            {
                e.Handled = false;
            }
            else
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// 添加界面里的确认添加或编辑按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_Add_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (Txt_RmNo.Text == null || Txt_RmNo.Text.Trim() == "")
                {
                    Txt_Option.Text = "房号不能为空";
                    Txt_Option.Visibility = Visibility.Visible;
                    Txt_RmNo.Focus();
                    return;
                }
                if (Txt_MealAreaSort.Text == null || Txt_MealAreaSort.Text.Trim() == "")
                {
                    Txt_Option.Text = "餐区排序不能为空";
                    Txt_Option.Visibility = Visibility.Visible;
                    Txt_MealAreaSort.Focus();
                    return;
                }
                if (Txt_FloorNumber.Text == null || Txt_FloorNumber.Text.Trim() == "")
                {
                    Txt_Option.Text = "楼层不能为空";
                    Txt_Option.Visibility = Visibility.Visible;
                    Txt_FloorNumber.Focus();
                    return;
                }
                if (Com_RtNames.SelectedItem == null)
                {
                    Txt_Option.Text = "房型名称不能为空";
                    Txt_Option.Visibility = Visibility.Visible;
                    return;
                }
                if (Com_AreaNames.SelectedItem == null)
                {
                    Txt_Option.Text = "区域名称不能为空";
                    Txt_Option.Visibility = Visibility.Visible;
                    return;
                }
                else
                {
                    MRmInfo Mr = new MRmInfo();
                    Mr.ShopId = (Com_Shops.SelectedItem as MShopInfo).ShopId;
                    Mr.AreaId = (Com_AreaNames.SelectedItem as MArea_MShop).AreaId;
                    Mr.RtNo = (Com_RtNames.SelectedItem as MRt_MShop).RtNo;
                    Mr.RmNo = Txt_RmNo.Text;
                    try
                    {
                        Mr.MealAreaSort = int.Parse(Txt_MealAreaSort.Text);
                        Mr.FloorNumber = int.Parse(Txt_FloorNumber.Text);
                    }
                    catch
                    {
                        Txt_Option.Text = "餐区排序和楼层都必须为整数";
                        Txt_Option.Visibility = Visibility.Visible;
                        return;
                    }
                    Mr.IsExistWc = (bool)Ck_IsExistWc.IsChecked;
                    Mr.IsDisplay = !(bool)Ck_IsDisplay.IsChecked;
                    if (Btn_Add.Tag.ToString() == "0")
                    {
                        int result = MRmInfoBll.GetMRmInfo_Add(Mr);
                        if (result > 0)
                        {
                            OperationLog OpLog = new OperationLog() { UserId = UserId, UserName = Username, TableName = "RmInfo（房间信息表）", types = "0", Conent = Mr.RmNo };
                            OperationLogBll.OperationLogAdd(OpLog);
                            MessageBox.Show("添加成功");
                            Txt_RmNo.Text = "";
                            Txt_MealAreaSort.Text = "";
                            Txt_FloorNumber.Text = "";
                            IniRmInfos = MRmInfoBll.GetMRmInfo(ShopId);
                            RmInfoByAlls();
                        }
                        else {
                            MessageBox.Show("添加失败");
                        }
                    }
                    else
                    {
                        int result = MRmInfoBll.GetMRmInfo_Udp(Mr, oldRmNo, oldShopId);
                        if (result > 0)
                        {
                            OperationLog OpLog = new OperationLog() { UserId = UserId, UserName = Username, TableName = "RmInfo（房间信息表）", types = "1", Conent = oldRmNo };
                            OperationLogBll.OperationLogAdd(OpLog);
                            Grid_AddUpd.Visibility = Visibility.Collapsed;
                            MessageBox.Show("编辑成功");
                            IniRmInfos = MRmInfoBll.GetMRmInfo(ShopId);
                            RmInfoByAlls();
                            Grid_All.Opacity = 1;
                        }
                        else {
                            MessageBox.Show("编辑失败");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// 添加界面的返回按钮事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_Cancel_Click(object sender, RoutedEventArgs e)
        {
            Grid_AddUpd.Visibility = Visibility.Collapsed;
            Grid_All.Opacity = 1;
        }

        /// <summary>
        /// 区域排序和楼层的值变更事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Txt_MealAreaSort_ValueChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            Txt_Option.Text = "";
            Txt_Option.Visibility = Visibility.Collapsed;
        }

    }
}
