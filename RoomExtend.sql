/*
 Navicat Premium Dump SQL

 Source Server         : 名堂
 Source Server Type    : SQL Server
 Source Server Version : 11002100 (11.00.2100)
 Source Host           : *************:1433
 Source Catalog        : Dbfood
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 11002100 (11.00.2100)
 File Encoding         : 65001

 Date: 16/07/2025 16:47:02
*/


-- ----------------------------
-- Table structure for RoomExtend
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[RoomExtend]') AND type IN ('U'))
	DROP TABLE [dbo].[RoomExtend]
GO

CREATE TABLE [dbo].[RoomExtend] (
  [RmNo] nvarchar(10) COLLATE Chinese_PRC_Stroke_CI_AS  NOT NULL,
  [ERmStatus] varchar(10) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT 'E' NOT NULL,
  [SoundCkMsg] nvarchar(50) COLLATE Chinese_PRC_Stroke_CI_AS  NULL,
  [SoundCkTime] datetime  NULL,
  [RmsUpTime] datetime DEFAULT getdate() NOT NULL,
  [MemberType] int DEFAULT 0 NOT NULL,
  [MemberNo] varchar(50) COLLATE Chinese_PRC_Stroke_CI_AS DEFAULT '' NOT NULL,
  [IsNotRmcost] bit DEFAULT 0 NOT NULL,
  [kfee] int DEFAULT 0 NOT NULL,
  [TotalPay] int DEFAULT 0 NULL,
  [NonMember] bit DEFAULT 0 NULL,
  [RmArea] int DEFAULT 0 NULL,
  [IntegralRule] int DEFAULT 0 NULL,
  [ReturnScale] int DEFAULT 0 NULL,
  [PointRule] int DEFAULT 0 NULL,
  [SuggestTime] datetime2(7)  NULL
)
GO

ALTER TABLE [dbo].[RoomExtend] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'天王变更rms最后时间',
'SCHEMA', N'dbo',
'TABLE', N'RoomExtend',
'COLUMN', N'RmsUpTime'
GO

EXEC sp_addextendedproperty
'MS_Description', N'会员号码类型如1：微信卡号，2：实体卡号',
'SCHEMA', N'dbo',
'TABLE', N'RoomExtend',
'COLUMN', N'MemberType'
GO


-- ----------------------------
-- Primary Key structure for table RoomExtend
-- ----------------------------
ALTER TABLE [dbo].[RoomExtend] ADD CONSTRAINT [PK_RoomExtend] PRIMARY KEY CLUSTERED ([RmNo])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

