using RMSDao;
using RMSDao.Dbfood;
using RMSModel.DbFood;
using RMSModel.RMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RMSBLL.DbFood
{
    public static class th_rms2019Bll
    {

        public static void SetConnection(string connection)
        {
            th_rms2019Dal.SetConnection(connection);
        }
        /// <summary>
        /// 续单
        /// </summary>
        /// <param name="RmNo">房间</param>
        /// <returns></returns>
        public static void Rm_Rm_Continue(string RmNo)
        {
            th_rms2019Dal.Rm_Rm_Continue(RmNo);

        }
        /// <summary>
        /// 获取所有的房间信息
        /// </summary>
        /// <returns></returns>
        public static List<Room> GetAllRoomInfo()
        {
            return th_rms2019Dal.GetAllRoomInfo();
        }
        /// <summary>
        /// 开房操作
        /// </summary>
        /// <param name="RmNo">房号</param>
        /// <param name="CustName">顾客姓名</param>
        /// <param name="Number">人数</param>
        /// <returns></returns>
        public static Room SetOpenRoom(string RmNo, string CustName, int Number, string rtNo, bool giftFromVip=false)
        {
            return th_rms2019Dal.SetOpenRoom(RmNo, CustName, Number, rtNo, giftFromVip);
        }
        /// <summary>
        /// 清洁操作
        /// </summary>
        /// <param name="RmNo">房号</param>

        /// <returns></returns>
        public static List<Room> SetClearingToEmptyRoom(string RmNo)
        {
            return th_rms2019Dal.SetClearingToEmptyRoom(RmNo);
        }
        /// <summary>
        /// 转房操作
        /// </summary>
        /// <param name="FromRmNo">转出房间</param>
        /// <param name="ToRmNo">转入房间</param>
        /// <returns></returns>
        public static List<Room> Rm_Exchange(string FromRmNo, string ToRmNo)
        {
            return th_rms2019Dal.Rm_Exchange(FromRmNo, ToRmNo);
        }
        /// <summary>
        /// 坏房标识
        /// </summary>
        /// <param name="RmNo">房间</param>
        /// <param name="Reason">原因</param>
        /// <returns></returns>
        public static void Rm_SetBadOn(string RmNo, string Reason)
        {
            th_rms2019Dal.Rm_SetBad(RmNo, true, Reason);
        }
        /// <summary>
        /// 取消坏房
        /// </summary>
        /// <param name="RmNo">房间</param>
        /// <param name="Reason">原因</param>
        /// <returns></returns>
        public static void Rm_SetBadOff(string RmNo)
        {
            th_rms2019Dal.Rm_SetBad(RmNo, false, "");
        }

        /// <summary>
        /// 全部清房
        /// </summary>
        /// <param name="RmNo">房间</param>
        /// <param name="IfSetBad">操作：true:坏房，false：取消坏房</param>
        /// <param name="Reason">原因</param>
        /// <returns></returns>
        public static void SetRoomClear(string room)
        {
            th_rms2019Dal.SetRoomClear(room);
        }

        /// <summary>
        /// 根据日期和手机号查询预约信息
        /// </summary>
        public static List<BookCacheInfo> GetBookInfo(string Date, string CusTel)
        {
            return th_rms2019Dal.GetBookInfo(Date, CusTel);
        }

        public static List<BookCacheInfo> GetBookInfo_all(string Date, string Shopid)
        {
            return th_rms2019Dal.GetBookInfo_all(Date, Shopid);

        }

        public static List<Room> GetRoom_All()
        {
            return th_rms2019Dal.GetRoom_All();
        }
        public static T trigger_synchro<T>()
        {
            return th_rms2019Dal.trigger_synchro<T>();
        }
        public static List<RmClose> GetLocalRmsRoom(string ShopId)
        {
            return th_rms2019Dal.GetLocalRmsRoom(ShopId);

        }
        /// <summary>
        /// 订金查询是否绑定
        /// </summary>
        /// <returns></returns>
        public static Room BindDepositCheck(string out_trade_no)
        {
            return th_rms2019Dal.BindDepositCheck(out_trade_no);

        }
        /// <summary>
        /// 订金绑定
        /// </summary>
        /// <returns></returns>
        public static int BindDeposit(int ShopId, string RmNo, string InvNo, int Tot, string transaction_id, string out_trade_no)
        {
            return th_rms2019Dal.BindDeposit(ShopId, RmNo, InvNo, Tot, transaction_id, out_trade_no);
        }
        /// <summary>
        /// 插入代订人记录
        /// </summary>
        /// <returns></returns>
        public static int SetInsertOrerUserInfo(Rms2018 rms2018)
        {
            return th_rms2019Dal.SetInsertOrerUserInfo(rms2018);
        }
        /// <summary>
        /// 设置RMS2009房间状态
        /// </summary>
        /// <returns></returns>
        public static void SetRms2009Status(string rmstatusnow, int ShopId, string rmno)
        {
            try
            {
                th_rms2019Dal.SetRms2009Status(rmstatusnow, ShopId, rmno);
            }
            catch
            {

            }
        }

        /// <summary>
        /// 获取当天所有开房数据
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.DbFood.FdInvInfo> GetRoomInfo(RMSModel.ReplaceBooking.OpenCacheInfo data)
        {
            return th_rms2019Dal.GetRoomInfo(data);
        }

        /// <summary>
        /// 执行转房操作（指定计费房型）- 简化版新增功能
        /// </summary>
        /// <param name="fromRmNo">转出房间号</param>
        /// <param name="toRmNo">转入房间号</param>
        /// <param name="billingRtNo">计费房型编号</param>
        /// <param name="userId">操作员ID</param>
        /// <param name="remarks">备注</param>
        /// <returns></returns>
        public static bool ExecuteRoomExchangeWithBillingType(string fromRmNo, string toRmNo, string billingRtNo, string userId, string remarks = "")
        {
            try
            {
                return th_rms2019Dal.ExecuteRoomExchangeWithBillingType(fromRmNo, toRmNo, billingRtNo, userId, remarks);
            }
            catch (Exception ex)
            {
                throw new Exception($"转房操作失败: {ex.Message}", ex);
            }
        }
    }
}
