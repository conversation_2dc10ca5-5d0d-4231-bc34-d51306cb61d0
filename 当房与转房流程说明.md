# 当房与转房功能流程说明

本文档旨在说明会员系统中“当房”和“转房”功能的设计、现有问题及解决方案。

## 1. 开房与“当房”

- **开房流程**: 指为新顾客办理入住手续的过程，主要逻辑位于 `WOpen.xaml.cs`。
- **“当房”**: 指顾客开房时确定的房型，此房型将作为后续消费的计费标准。例如，顾客预订的是豪华大床房，即使因为某些原因被安排到普通大床房，计费也应按照豪华大床房的标准。这个最初确定的计费房型就是“当房”。
- **数据记录**: 开房时，系统会将包括“当房”信息（如 `RtNo`, `RtName`）在内的所有入住详情记录到 `OpenCacheInfo` 表中。这张表是整个入住期间所有消费和操作的核心依据，其中的 `RoomToIkey` 是该次入住的唯一标识。

## 2. 转房

- **转房流程**: 指将已入住的顾客从一个房间更换到另一个房间。核心功能由 `RoomChange.xaml.cs` 用户控件处理。
- **核心方法**: `RmExchangeAdd` 方法是执行转房操作的核心。

## 3. 问题描述：中途/转房当房未实现

- **问题现象**: 在进行转房操作时，系统虽然成功地将顾客更换到了新的房间，但后续的计费并未按照最初开房时约定的“当房”标准进行，导致账单错误。
- **根本原因（更新后）**: 经过对数据库存储过程 `GetMRmInfo_Udp` 的分析，我们发现问题的根源并非C#代码的遗漏，而是**数据库层面的逻辑与业务需求不符**。
	- 该存储过程在执行转房时，会更新 `OpenCacheInfo` 表。
	- 但它不仅更新了顾客所在的物理房号（`RmNo`），**还错误地将计费房型（`RtNo` 和 `RtName`）也更新为了新房间的房型**。
	- 这意味着，顾客的计费标准会随着转房而改变，违背了“当房”的核心原则——即计费标准应在整个入住期间保持不变。

## 4. 核心矛盾

当前系统存在一个明显的业务逻辑冲突：

- **业务需求**: 顾客转房后，应继续按照最初开房时约定的**原房型（当房）**进行计费。
- **系统实现**: 数据库存储过程 `GetMRmInfo_Udp` 的逻辑是，顾客转房后，按照**新房间的房型**进行计费。

这个矛盾是导致“中途/转房当房未实现”的根本原因。

## 5. 解决方案（建议）

正确的解决方案应该是修改数据库存储过程 `GetMRmInfo_Udp`。

- **修改建议**: 调整其中的 `UPDATE OpenCacheInfo` 语句，**只更新物理房号 `RmNo` 和转房来源 `FromRmNo`**，而保持计费房型 `RtNo` 和 `RtName` 不变。

  ```sql
  -- 建议的修改 (示意)
  update OpenCacheInfo set FromRmNo=@RoomFromNo, RmNo=@RoomToNo where Ikey=@RoomIkey
  ```

- **同时**，应移除之前在 C# 代码 `RoomChange.xaml.cs` 中为修复此问题而添加的 `OpenCacheInfoBll.UpdateRmNo(...)` 调用，以避免逻辑冗余和潜在冲突。

  ```csharp
  // jjy-20250711 更新OpenCacheInfo中的房号，以确保转房后当房信息正确
  OpenCacheInfoBll.UpdateRmNo(FromRmInfo.RoomToIkey, EW.ToRmNo);
  ```

- **逻辑说明**:
  1. `FromRmInfo.RoomToIkey` 获取了本次入住的唯一ID。
  2. `EW.ToRmNo` 是顾客转入的新房号。
  3. 通过调用 `OpenCacheInfoBll.UpdateRmNo` 方法，我们使用唯一ID定位到 `OpenCacheInfo` 表中正确的开房记录，并将其中的房号（`RmNo`）更新为顾客所在的新房号。

- **最终效果**: 通过此修改，我们确保了 `OpenCacheInfo` 表中的数据始终能反映顾客的最新位置，同时保留了最初的“当房”计费信息（`RtNo`, `RtName`）。这样，无论顾客换到哪个房间，系统都能准确地按照约定的标准进行计费，解决了“中途/转房当房未实现”的问题。
