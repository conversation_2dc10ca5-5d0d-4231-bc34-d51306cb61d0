﻿<UserControl x:Class="RMS2018.ComponentWpf.LoginPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300"  Height="680" Width="900" >
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="367*"/>
            <ColumnDefinition Width="55*"/>
        </Grid.ColumnDefinitions>
        <Grid Grid.ColumnSpan="2">
            <Canvas>
                <Button Content="登录" Name="But_Login" HorizontalAlignment="Left" VerticalAlignment="Top" Width="75" Click="Button_Click" IsDefault="True" Canvas.Left="281" Canvas.Top="243" />
                <TextBox HorizontalAlignment="Left" Text="9900" Name="Txt_UserId" Height="23" TextWrapping="Wrap"  VerticalAlignment="Top" Width="169" Canvas.Left="151" Canvas.Top="64"/>
                <PasswordBox HorizontalAlignment="Left" Name="Txt_UserPwd" VerticalAlignment="Top" Width="169" Canvas.Left="151" Canvas.Top="140" Password="123"/>
                <TextBlock HorizontalAlignment="Left" TextWrapping="Wrap" Text="工号：" VerticalAlignment="Top" Canvas.Left="75" Canvas.Top="64"/>
                <TextBlock HorizontalAlignment="Left" TextWrapping="Wrap" Text="密码：" VerticalAlignment="Top" RenderTransformOrigin="0.545,3.25" Canvas.Left="75" Canvas.Top="140"/>
                <Button Content="注册" Name="But_Regist" HorizontalAlignment="Left" VerticalAlignment="Top" Width="75" Click="But_Regist_Click_1" IsDefault="True" Canvas.Left="20" Canvas.Top="243" />


            </Canvas>
            <Button x:Name="btnOpen" Content="开房" HorizontalAlignment="Left" Margin="107,184,0,0" VerticalAlignment="Top" Width="75" Click="btnOpen_Click"/>
            <Button x:Name="btnBook" Content="预订" HorizontalAlignment="Left" Margin="209,184,0,0" VerticalAlignment="Top" Width="75" Click="btnBook_Click"/>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"></RowDefinition>
                    <RowDefinition Height="93*"></RowDefinition>
                    <RowDefinition Height="7*"></RowDefinition>
                </Grid.RowDefinitions>
                <Grid Background="#EDF1F5" >

                    <Grid   HorizontalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="13*"/>
                            <ColumnDefinition Width="17*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock x:Name="textBlock" HorizontalAlignment="Left" Margin="10,10,0,0" TextWrapping="Wrap" FontSize="18" FontWeight="Bold" Text="" VerticalAlignment="Top"/>
                        <TextBlock HorizontalAlignment="Left" Margin="107,10,0,0" TextWrapping="Wrap"  Text="*首次使用，必须点击下方注册新用户按钮进行注册激活。" Foreground="#FFA43939" VerticalAlignment="Top" Grid.ColumnSpan="2"/>
                        <TextBox x:Name="tbUserId" HorizontalAlignment="Left" Height="23" Margin="39,35,0,0" TextWrapping="Wrap" Text="" VerticalAlignment="Top" Width="139"  KeyDown="tbUserId_KeyUp"/>
                        <PasswordBox x:Name="tbUserPwd" HorizontalAlignment="Left" Height="23" Margin="32.333,35,0,0" VerticalAlignment="Top" Width="139" Grid.Column="1" KeyDown="tbUserPwd_KeyDown"/>
                        <TextBlock x:Name="textBlock_Copy" HorizontalAlignment="Left" Margin="10,38,0,0" TextWrapping="Wrap" Text="工号" VerticalAlignment="Top"/>
                        <TextBlock x:Name="textBlock_Copy1" HorizontalAlignment="Left" Margin="3.333,38,0,0" TextWrapping="Wrap" Text="密码" VerticalAlignment="Top" Grid.Column="1"/>
                        <Button x:Name="btnLogin" Click="btnLogin_Click" Background="#FF27B419" Foreground="White" Content="登录" HorizontalAlignment="Left" Margin="176.333,35,0,0" VerticalAlignment="Top" Width="79" Height="22" Grid.Column="1"/>
                        <Button x:Name="btnUpdatePassword"  Background="#4395ff" Foreground="White" Content="修改密码" HorizontalAlignment="Left" Margin="283,35,-107,0" VerticalAlignment="Top" Width="79" Height="22" Grid.Column="1" Click="btnUpdatePassword_Click"/>

                    </Grid>
                    <Rectangle Fill="#FFDFDFE4" Stroke="Black" VerticalAlignment="Bottom"/>

                </Grid>
                <WebBrowser  Name="web" Grid.Row="1" />
                <Grid Grid.Row="1" Background="#FF94C0D1" Visibility="Hidden">
                    <TextBlock   TextAlignment="Center" VerticalAlignment="Center" FontSize="30" Foreground="White">扫码登录已下架</TextBlock>
                </Grid>
                <Button Grid.Row="2" Name="btnCode" Click="btnCode_Click">注册新用户</Button>

                <Grid Name="gridload" Background="Black" Visibility="Collapsed">
                    <TextBlock VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="White" FontSize="20"> 数据正在初始化</TextBlock>
                </Grid>
            </Grid>
        </Grid>

    </Grid>
</UserControl>
