﻿using RMSBLL.birthdaygiftPay;
using RMSModel.birthdaygiftPay;
using RMSUtils.RMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace RMS2018.birthday_giftPay
{
    /// <summary>
    /// birthdaygiftPayManageWindow.xaml 的交互逻辑
    /// </summary>
    public partial class birthdaygiftPayManageWindow : Window
    {
        public birthdaygiftPayManageWindow()
        {
            InitializeComponent();
        }
        /// <summary>
        /// 加载事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Window_Loaded_1(object sender, RoutedEventArgs e)
        {

            rbModel1.IsChecked = true;
            ntTopNumber.Text = "30";
            ntTopNumber.Number = 30;
            ShopId = SingleRun.GetSingle().vmpc.vmuserinfo.ShopId;
            SetData();
           
        }

        int ShopId = 0;
        /// <summary>
        ///获取，绑定数据 
        /// </summary>
        public void SetData()
        {
            loadico.Visibility = Visibility.Visible;
            spRadioButton.IsEnabled = false;
            var ComeDate = VMTimeDate.GetWordDate().ToString("yyyyMMdd");
            //var ComeDate = DateTime.Now.AddDays(-5).ToString("yyyyMMdd");
            int num = ntTopNumber.Number;
            birthday_giftDataBll _bll = new birthday_giftDataBll();
            List<birthday_giftData> data = new List<birthday_giftData>();
            loadico.Visibility = Visibility.Visible;
            spRadioButton.IsEnabled = false;
            Thread thread = new Thread(delegate()
            {
                try
                {
                    data = _bll.getData(num, ModelSelect, ShopId, ComeDate);
                    this.Dispatcher.Invoke(new Action(() =>
                    {
                        loadico.Visibility = Visibility.Collapsed;
                        spRadioButton.IsEnabled = true;
                        BtnBingDeposit.IsEnabled = false;
                        dgDeposit.ItemsSource = data;
                    }));
                }
                catch (Exception ex)
                {
                    this.Dispatcher.Invoke(new Action(() =>
                    {
                        loadico.Visibility = Visibility.Collapsed;
                        spRadioButton.IsEnabled = true;

                    }));
                    MessageBox.Show("获取生日赠送支付记录erro:" + ex.Message);
                }
            });
            thread.Start();

        }
        int ModelSelect = 0;
        /// <summary>
        /// 单选按钮选中
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void rbModel1_Checked_1(object sender, RoutedEventArgs e)
        {
            RadioButton radio = sender as RadioButton;
            if (radio != null)
            {
                ModelSelect = int.Parse(radio.Tag.ToString());
                SetData();
            }
        }
        /// <summary>
        /// 绑定定金按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnBingDeposit_Click_1(object sender, RoutedEventArgs e)
        {
            try
            {
                var data = dgDeposit.SelectedItem as birthday_giftData;
                if (data == null)
                {
                    MessageBox.Show("请选择操作数据！");
                }
                else
                {
                    birthday_giftDataBll _bll = new birthday_giftDataBll();

                    opencacheinfo OpenData = _bll.getOpencacheinfo(data.Ikey);
                    if (!string.IsNullOrEmpty(OpenData.Invno))
                    {
                        var objdata = _bll.SetWxPayInfo(ShopId, data.RmNo, OpenData.Invno, data.DepositTot*100, data.transaction_id, data.transaction_id);
                        //var objdata = _bll.SetWxPayInfo(ShopId, data.RmNo, "A01967749", 200, data.transaction_id + "11", data.transaction_id + "11");
                        if (objdata.ReturnVal == 0)
                        {
                            _bll.SetData(data.Ikey);
                            SetData();
                            MessageBox.Show("成功");
                        }
                        else {
                            MessageBox.Show("已经绑定，请刷新页面");
                        }
                    }
                    else {
                         MessageBox.Show("获取不到房间账单号，请联系管理员！");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("绑定定金失败error:" + ex.Message);
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgDeposit_MouseLeftButtonUp_1(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var data = dgDeposit.SelectedItem as birthday_giftData;
                if (data != null)
                {
                    if (!string.IsNullOrEmpty(data.RmNo))
                    {
                        BtnBingDeposit.IsEnabled = true;
                    }
                    else {
                        BtnBingDeposit.IsEnabled = false ;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("获取选中数据error：" + ex.Message);
            }
        }
        /// <summary>
        /// 刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnRefresh_Click_1(object sender, RoutedEventArgs e)
        {
            SetData();
        }
        /// <summary>
        /// 返回
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnClose_Click_1(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
