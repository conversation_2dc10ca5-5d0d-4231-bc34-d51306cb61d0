﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Controls;

namespace RMS2018.ComponentWpf.ViewModel
{
    /// <summary>
    /// 主页面viewModel
    /// </summary>
    public class MainPageViewModel : PageBaseViewModel
    {
        public MainPage Page;
        public MainPageViewModel(MainPage page)
        {
            this.Page = page;
            OnInit();
        }


        public override void OnInit()
        {
            /////清空页面表格
            //Page.tbControl.Items.Clear();

            /////初始化页面表格
            //TabItem item = new TabItem();
            //item.Header = "预约11111";
            //item.Content = new BookPage();
            //Page.tbControl.Items.Add(item);
        }
    }
}
