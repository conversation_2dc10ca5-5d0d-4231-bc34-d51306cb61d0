﻿<Window x:Class="RMS2018.birthday_giftPay.birthdaygiftPayManageWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
         xmlns:Controls="clr-namespace:RMS2018.Controls" xmlns:Loading="clr-namespace:RMS2018.Loading" 
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        
         Title="生日蛋糕支付订金管理" Height="750" Width="1300" WindowState="Maximized" WindowStartupLocation="CenterScreen" Icon="pack://siteoforigin:,,,/Resources/money.png" Loaded="Window_Loaded_1">
    <Window.Resources>
        <Style TargetType="{x:Type RadioButton}">
            <Setter Property="FontSize" Value="15"/>
            <Setter Property="Margin" Value="10,5"/>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition/>
            <RowDefinition Height="45"/>
        </Grid.RowDefinitions>
        <Grid Background="#3301B301"/>
        <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Name="spRadioButton">
            <RadioButton Tag="0" Checked="rbModel1_Checked_1"  x:Name="rbModel1"  Content="未消费(今天)"  />
            <RadioButton Tag="4" Checked="rbModel1_Checked_1" Content="未消费（其它日期）"  />
            <RadioButton Tag="3" Checked="rbModel1_Checked_1"  Content="已消费"/>
            <RadioButton Tag="1" Checked="rbModel1_Checked_1" Content="已过期"/>
            <RadioButton Tag="2" Checked="rbModel1_Checked_1" Content="派房（暂时未确定）"/>
            <TextBlock VerticalAlignment="Center" Margin="50,0,5,0">展示前</TextBlock>
            <Controls:NumbiricTextBox VerticalContentAlignment="Center" Width="30" TextWrapping="Wrap"  x:Name="ntTopNumber"/>
            <TextBlock VerticalAlignment="Center" Margin="5,0,5,0">条数据</TextBlock>
        </StackPanel>


        <DataGrid  x:Name="dgDeposit"  IsReadOnly="True" AutoGenerateColumns="False" Grid.Row="1" MouseLeftButtonUp="dgDeposit_MouseLeftButtonUp_1" >
            <DataGrid.Columns>
                <DataGridTextColumn Width="*" Header="房号"   Binding="{Binding RmNo}"/>
                <DataGridTextColumn Width="*" Header="支付时间"   Binding="{Binding BookDateTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}"/>
                <DataGridTextColumn Width="*" Header="消费日期"   Binding="{Binding ComeDate}"/>
                <DataGridTextColumn Width="*" Header="消费时间"   Binding="{Binding ComeTime}"/>
                <DataGridTextColumn Width="*" Header="顾客姓名"   Binding="{Binding CustName}"/>
                <DataGridTextColumn Width="*" Header="联系电话"   Binding="{Binding CustTel}"/>
                <DataGridTextColumn Width="*" Header="预约号码"   Binding="{Binding BookNo}"/>
                <DataGridTextColumn Width="*" Header="订金金额"   Binding="{Binding DepositTot}"/>
                <DataGridTextColumn Width="250" Header="账单号码"   Binding="{Binding transaction_id}"/>
                <DataGridTextColumn Width="250" Header="状态"   Binding="{Binding DepositStatus}"/>
            </DataGrid.Columns>
        </DataGrid>
        <Controls:LoadingWait  Grid.Row="1"  x:Name="loadico"  Visibility="Collapsed"/>
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Grid.Row="2">
            <!--<Button Padding="5" Margin="5" Name="BtnRefund" Background="#FFC61212" Foreground="White"  Click="BtnRefundClick"  Content="标识为已退款"/>-->
            <Button Padding="5" Margin="5" x:Name="BtnBingDeposit" Background="#FF2DC212" Foreground="White" Click="BtnBingDeposit_Click_1"  IsEnabled="False" Content="绑定订金"/>
            <Button Padding="5" Margin="5" x:Name="BtnRefresh" Click="BtnRefresh_Click_1"  Content="刷新"/>
            <Button Padding="5" Margin="5" x:Name="btnClose" Click="btnClose_Click_1"  Content="返回"/>
        </StackPanel>

    </Grid>
</Window>
